# AI任务执行清单-资产盘点前端开发

## 项目概述

基于RuoYi框架和Vue.js + Element UI技术栈，开发完整的资产盘点功能前端模块，与已有的后端Controller接口进行对接。

## 技术环境

- **前端框架**: Vue.js 2.x + Element UI
- **构建工具**: Vue CLI / Webpack
- **状态管理**: Vuex
- **路由管理**: Vue Router
- **HTTP客户端**: Axios
- **代码规范**: ESLint + Prettier

## 前端目录结构设计

```
device_monitor-ui/src/views/assets/stocktaking/
├── plan/                           # 盘点计划管理
│   ├── index.vue                  # 盘点计划列表页面
│   ├── add.vue                    # 新增盘点计划页面
│   ├── edit.vue                   # 编辑盘点计划页面
│   └── detail.vue                 # 盘点计划详情页面
├── task/                          # 盘点任务管理
│   ├── index.vue                  # 盘点任务列表页面
│   ├── execute.vue                # 执行盘点任务页面
│   ├── progress.vue               # 盘点进度监控页面
│   └── assign.vue                 # 任务分配页面
├── record/                        # 盘点记录管理
│   ├── index.vue                  # 盘点记录列表页面
│   ├── scan.vue                   # 扫码盘点页面
│   ├── manual.vue                 # 手动录入页面
│   └── difference.vue             # 差异分析页面
├── report/                        # 盘点报告
│   ├── index.vue                  # 报告列表页面
│   ├── summary.vue                # 汇总报告页面
│   ├── detail.vue                 # 详细报告页面
│   └── chart.vue                  # 图表分析页面
└── components/                    # 共用组件
    ├── AssetScanner.vue           # 资产扫码组件
    ├── ProgressChart.vue          # 进度图表组件
    ├── PlanSelector.vue           # 计划选择器组件
    ├── TaskCard.vue               # 任务卡片组件
    └── StatisticsPanel.vue        # 统计面板组件
```

## API接口映射

### 盘点计划接口 (/asset/stocktaking/plan)
- `POST /list` - 查询盘点计划列表
- `GET /{planId}` - 获取盘点计划详情
- `POST /` - 新增盘点计划
- `PUT /` - 修改盘点计划
- `DELETE /{planIds}` - 删除盘点计划
- `POST /{planId}/submit` - 提交审批
- `POST /{planId}/approve` - 审批通过
- `POST /{planId}/reject` - 审批拒绝

### 盘点任务接口 (/asset/stocktaking/task)
- `POST /list` - 查询盘点任务列表
- `GET /{taskId}` - 获取盘点任务详情
- `POST /` - 新增盘点任务
- `PUT /` - 修改盘点任务
- `DELETE /{taskIds}` - 删除盘点任务
- `POST /distribute/{planId}` - 分发盘点任务
- `POST /{taskId}/claim` - 领取盘点任务
- `POST /{taskId}/start` - 开始执行任务
- `POST /{taskId}/complete` - 完成任务

### 盘点记录接口 (/asset/stocktaking/record)
- `POST /list` - 查询盘点记录列表
- `GET /{recordId}` - 获取盘点记录详情
- `POST /` - 新增盘点记录
- `PUT /` - 修改盘点记录
- `DELETE /{recordIds}` - 删除盘点记录
- `POST /scan` - 扫码盘点
- `POST /manual` - 手动录入盘点结果
- `GET /task/{taskId}` - 根据任务ID查询记录
- `GET /abnormal/{taskId}` - 查询异常记录

### 盘点报告接口 (/asset/stocktaking/report)
- `GET /summary/{planId}` - 生成盘点汇总报告
- `GET /difference/{planId}` - 生成差异明细报告
- `GET /dept/{planId}` - 生成部门统计报告
- `GET /progress/{planId}` - 生成盘点进度报告
- `GET /chart/{planId}` - 生成盘点统计图表数据

## 详细任务分解

### 阶段1: 基础设施搭建 (优先级: 最高)

#### 任务1.1: 创建路由配置
**执行动作**: 修改路由文件
**文件路径**: `device_monitor-ui/src/router/index.js`
**技术要求**:
- 添加资产盘点模块路由配置
- 配置权限控制 (perms)
- 设置面包屑导航
- 配置菜单图标和标题

#### 任务1.2: 创建API接口文件
**执行动作**: 创建JavaScript文件
**文件路径**: `device_monitor-ui/src/api/assets/stocktaking.js`
**技术要求**:
- 封装所有盘点相关API接口
- 使用统一的request方法
- 添加接口注释说明
- 支持参数验证

#### 任务1.3: 创建共用组件基础结构
**执行动作**: 创建Vue组件文件
**文件路径**: `device_monitor-ui/src/views/assets/stocktaking/components/`
**技术要求**:
- 创建基础组件框架
- 定义组件props和events
- 添加基础样式

### 阶段2: 盘点计划管理模块 (优先级: 最高)

#### 任务2.1: 创建盘点计划列表页面
**执行动作**: 创建Vue文件
**文件路径**: `device_monitor-ui/src/views/assets/stocktaking/plan/index.vue`
**功能要求**:
- 盘点计划列表查询和展示
- 搜索条件筛选 (计划名称、状态、日期范围)
- 分页显示
- 操作按钮 (新增、编辑、删除、查看详情)
- 批量操作功能
- 状态标签显示
- Excel导出功能

#### 任务2.2: 创建新增盘点计划页面
**执行动作**: 创建Vue文件
**文件路径**: `device_monitor-ui/src/views/assets/stocktaking/plan/add.vue`
**功能要求**:
- 盘点计划基本信息表单
- 盘点范围设置 (部门、位置、资产类别)
- 负责人选择
- 计划日期设置
- 表单验证
- 提交和重置功能

#### 任务2.3: 创建编辑盘点计划页面
**执行动作**: 创建Vue文件
**文件路径**: `device_monitor-ui/src/views/assets/stocktaking/plan/edit.vue`
**功能要求**:
- 复用新增页面组件
- 数据回显功能
- 状态控制 (不同状态下的可编辑字段)
- 审批流程操作

#### 任务2.4: 创建盘点计划详情页面
**执行动作**: 创建Vue文件
**文件路径**: `device_monitor-ui/src/views/assets/stocktaking/plan/detail.vue`
**功能要求**:
- 计划基本信息展示
- 关联任务列表
- 执行进度展示
- 操作日志记录
- 审批历史记录

### 阶段3: 盘点任务管理模块 (优先级: 高)

#### 任务3.1: 创建盘点任务列表页面
**执行动作**: 创建Vue文件
**文件路径**: `device_monitor-ui/src/views/assets/stocktaking/task/index.vue`
**功能要求**:
- 任务列表查询和展示
- 任务状态筛选
- 分配人员筛选
- 任务进度显示
- 操作按钮 (领取、开始、完成、重新分配)

#### 任务3.2: 创建任务执行页面
**执行动作**: 创建Vue文件
**文件路径**: `device_monitor-ui/src/views/assets/stocktaking/task/execute.vue`
**功能要求**:
- 任务基本信息展示
- 资产列表展示
- 盘点记录录入
- 扫码功能集成
- 进度实时更新

#### 任务3.3: 创建进度监控页面
**执行动作**: 创建Vue文件
**文件路径**: `device_monitor-ui/src/views/assets/stocktaking/task/progress.vue`
**功能要求**:
- 整体进度展示
- 任务完成情况统计
- 进度图表展示
- 异常任务提醒

### 阶段4: 盘点记录管理模块 (优先级: 高)

#### 任务4.1: 创建盘点记录列表页面
**执行动作**: 创建Vue文件
**文件路径**: `device_monitor-ui/src/views/assets/stocktaking/record/index.vue`
**功能要求**:
- 记录列表查询和展示
- 多条件筛选
- 异常记录标识
- 记录详情查看

#### 任务4.2: 创建扫码盘点页面
**执行动作**: 创建Vue文件
**文件路径**: `device_monitor-ui/src/views/assets/stocktaking/record/scan.vue`
**功能要求**:
- 扫码功能集成
- 资产信息自动填充
- 盘点结果录入
- 批量扫码支持

#### 任务4.3: 创建手动录入页面
**执行动作**: 创建Vue文件
**文件路径**: `device_monitor-ui/src/views/assets/stocktaking/record/manual.vue`
**功能要求**:
- 资产信息手动输入
- 盘点结果录入
- 批量录入支持
- 数据验证

#### 任务4.4: 创建差异分析页面
**执行动作**: 创建Vue文件
**文件路径**: `device_monitor-ui/src/views/assets/stocktaking/record/difference.vue`
**功能要求**:
- 差异记录展示
- 差异类型分类
- 差异原因录入
- 处理建议展示

### 阶段5: 盘点报告模块 (优先级: 中)

#### 任务5.1: 创建报告列表页面
**执行动作**: 创建Vue文件
**文件路径**: `device_monitor-ui/src/views/assets/stocktaking/report/index.vue`
**功能要求**:
- 报告列表展示
- 报告类型筛选
- 报告生成功能
- 报告下载功能

#### 任务5.2: 创建汇总报告页面
**执行动作**: 创建Vue文件
**文件路径**: `device_monitor-ui/src/views/assets/stocktaking/report/summary.vue`
**功能要求**:
- 盘点汇总数据展示
- 统计图表展示
- 数据导出功能

#### 任务5.3: 创建详细报告页面
**执行动作**: 创建Vue文件
**文件路径**: `device_monitor-ui/src/views/assets/stocktaking/report/detail.vue`
**功能要求**:
- 详细数据展示
- 多维度分析
- 自定义报告配置

#### 任务5.4: 创建图表分析页面
**执行动作**: 创建Vue文件
**文件路径**: `device_monitor-ui/src/views/assets/stocktaking/report/chart.vue`
**功能要求**:
- 多种图表类型支持
- 交互式图表展示
- 数据钻取功能

### 阶段6: 共用组件开发 (优先级: 中)

#### 任务6.1: 创建资产扫码组件
**执行动作**: 创建Vue组件
**文件路径**: `device_monitor-ui/src/views/assets/stocktaking/components/AssetScanner.vue`
**功能要求**:
- 支持多种扫码方式 (二维码、条形码)
- 扫码结果处理
- 错误处理和提示
- 移动端适配

#### 任务6.2: 创建进度图表组件
**执行动作**: 创建Vue组件
**文件路径**: `device_monitor-ui/src/views/assets/stocktaking/components/ProgressChart.vue`
**功能要求**:
- 进度条展示
- 环形进度图
- 数据动画效果
- 响应式设计

#### 任务6.3: 创建计划选择器组件
**执行动作**: 创建Vue组件
**文件路径**: `device_monitor-ui/src/views/assets/stocktaking/components/PlanSelector.vue`
**功能要求**:
- 计划下拉选择
- 搜索功能
- 状态筛选
- 数据懒加载

#### 任务6.4: 创建任务卡片组件
**执行动作**: 创建Vue组件
**文件路径**: `device_monitor-ui/src/views/assets/stocktaking/components/TaskCard.vue`
**功能要求**:
- 任务信息展示
- 状态标识
- 操作按钮
- 响应式布局

#### 任务6.5: 创建统计面板组件
**执行动作**: 创建Vue组件
**文件路径**: `device_monitor-ui/src/views/assets/stocktaking/components/StatisticsPanel.vue`
**功能要求**:
- 统计数据展示
- 图标和数字展示
- 颜色主题支持
- 动画效果

## 技术规范要求

### 代码规范
- 遵循Vue.js官方风格指南
- 使用ESLint进行代码检查
- 组件命名采用PascalCase
- 文件命名采用kebab-case
- 添加完整的注释说明

### UI/UX规范
- 遵循Element UI设计规范
- 保持与现有系统界面风格一致
- 响应式设计，支持移动端访问
- 操作流程清晰，用户体验友好
- 重要操作需要确认提示

### 性能要求
- 页面首次加载时间 < 3秒
- 列表查询响应时间 < 2秒
- 支持大数据量展示 (分页、虚拟滚动)
- 图片和资源懒加载
- 合理使用缓存机制

### 兼容性要求
- 支持主流浏览器 (Chrome、Firefox、Safari、Edge)
- 移动端浏览器兼容
- 不同屏幕尺寸适配
- 支持触摸操作

## 验收标准

### 功能验收
- [ ] 所有页面功能正常运行
- [ ] 与后端接口对接成功
- [ ] 用户操作流程完整
- [ ] 数据展示准确无误
- [ ] 权限控制正确实施

### 技术验收
- [ ] 代码通过ESLint检查
- [ ] 组件可复用性良好
- [ ] 性能指标达到要求
- [ ] 兼容性测试通过
- [ ] 无明显bug和异常

### UI验收
- [ ] 界面美观，符合设计规范
- [ ] 交互体验流畅
- [ ] 响应式布局正确
- [ ] 移动端适配良好
- [ ] 无明显样式问题

## 开发注意事项

### 与现有系统集成
- 复用现有的权限控制机制
- 使用统一的API请求封装
- 保持路由命名规范一致
- 集成现有的字典数据管理

### 扩展性考虑
- 组件设计要考虑复用性
- 预留移动端扩展接口
- 支持多语言扩展
- 考虑后续功能迭代

### 安全性要求
- 前端数据验证
- 敏感操作确认
- XSS攻击防护
- 权限验证

## 具体实现指导

### 数据结构定义

#### 盘点计划数据结构
```javascript
// 盘点计划查询参数
const planQueryParams = {
  pageNum: 1,
  pageSize: 10,
  planName: '',
  planType: null,
  status: null,
  startDate: '',
  endDate: '',
  responsibleUserId: null
}

// 盘点计划表单数据
const planFormData = {
  planId: '',
  planName: '',
  planType: 1, // 1-全盘，2-部分盘点
  planScope: '', // JSON格式
  startDate: '',
  endDate: '',
  responsibleUserId: null,
  remark: ''
}
```

#### 盘点任务数据结构
```javascript
// 盘点任务查询参数
const taskQueryParams = {
  pageNum: 1,
  pageSize: 10,
  planId: '',
  taskName: '',
  assignedUserId: null,
  status: null
}

// 盘点任务表单数据
const taskFormData = {
  taskId: '',
  planId: '',
  taskName: '',
  assignedUserId: null,
  assetScope: '', // JSON格式
  expectedCount: 0,
  distributionConfig: {
    distributionType: 1,
    maxAssetCount: 100,
    assignedUserIds: [],
    autoAssign: true,
    priority: 2
  }
}
```

#### 盘点记录数据结构
```javascript
// 盘点记录查询参数
const recordQueryParams = {
  pageNum: 1,
  pageSize: 10,
  taskId: '',
  assetId: '',
  assetCode: '',
  foundStatus: null,
  inventoryUserId: null,
  startTime: '',
  endTime: ''
}

// 盘点记录表单数据
const recordFormData = {
  recordId: '',
  taskId: '',
  assetId: '',
  assetCode: '',
  foundStatus: 1, // 1-找到，0-未找到
  actualLocation: '',
  actualStatus: null,
  inventoryTime: '',
  remark: '',
  scanInfo: {
    scanType: 1, // 1-二维码，2-条形码，3-RFID
    scanContent: '',
    scanTime: '',
    deviceInfo: ''
  }
}
```

### 组件通信规范

#### 父子组件通信
```javascript
// 父组件向子组件传递数据
props: {
  planData: {
    type: Object,
    required: true
  },
  readonly: {
    type: Boolean,
    default: false
  }
}

// 子组件向父组件发送事件
this.$emit('plan-updated', planData)
this.$emit('task-completed', taskId)
this.$emit('scan-result', scanData)
```

#### 兄弟组件通信
```javascript
// 使用事件总线
this.$bus.$emit('refresh-task-list')
this.$bus.$on('refresh-task-list', this.loadTaskList)

// 使用Vuex状态管理
this.$store.dispatch('stocktaking/updatePlanStatus', { planId, status })
this.$store.commit('stocktaking/SET_CURRENT_TASK', taskData)
```

### 权限控制实现

#### 页面级权限控制
```javascript
// 在路由配置中添加权限
{
  path: '/assets/stocktaking/plan',
  component: () => import('@/views/assets/stocktaking/plan/index'),
  meta: {
    title: '盘点计划',
    perms: ['stocktaking:plan:view']
  }
}

// 在组件中检查权限
computed: {
  hasAddPermission() {
    return this.checkPermi(['stocktaking:plan:add'])
  },
  hasEditPermission() {
    return this.checkPermi(['stocktaking:plan:edit'])
  }
}
```

#### 按钮级权限控制
```vue
<template>
  <el-button
    v-hasPermi="['stocktaking:plan:add']"
    type="primary"
    @click="handleAdd">
    新增计划
  </el-button>
</template>
```

### 表单验证规则

#### 盘点计划表单验证
```javascript
const planRules = {
  planName: [
    { required: true, message: '计划名称不能为空', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  planType: [
    { required: true, message: '请选择盘点类型', trigger: 'change' }
  ],
  startDate: [
    { required: true, message: '请选择开始日期', trigger: 'change' }
  ],
  endDate: [
    { required: true, message: '请选择结束日期', trigger: 'change' }
  ],
  responsibleUserId: [
    { required: true, message: '请选择负责人', trigger: 'change' }
  ]
}
```

### 错误处理机制

#### API请求错误处理
```javascript
async loadPlanList() {
  this.loading = true
  try {
    const response = await listStocktakingPlan(this.queryParams)
    this.planList = response.rows
    this.total = response.total
  } catch (error) {
    this.$modal.msgError('查询失败：' + error.message)
  } finally {
    this.loading = false
  }
}
```

#### 表单提交错误处理
```javascript
async submitForm() {
  try {
    await this.$refs.form.validate()
    const response = await addStocktakingPlan(this.form)
    this.$modal.msgSuccess('新增成功')
    this.$router.push('/assets/stocktaking/plan')
  } catch (error) {
    if (error.fields) {
      // 表单验证错误
      this.$modal.msgError('请检查表单输入')
    } else {
      // API请求错误
      this.$modal.msgError('提交失败：' + error.message)
    }
  }
}
```

### 状态管理设计

#### Vuex Store结构
```javascript
// store/modules/stocktaking.js
const state = {
  currentPlan: null,
  currentTask: null,
  planList: [],
  taskList: [],
  recordList: [],
  statistics: {}
}

const mutations = {
  SET_CURRENT_PLAN(state, plan) {
    state.currentPlan = plan
  },
  SET_CURRENT_TASK(state, task) {
    state.currentTask = task
  },
  SET_PLAN_LIST(state, list) {
    state.planList = list
  }
}

const actions = {
  async loadPlanList({ commit }, params) {
    const response = await listStocktakingPlan(params)
    commit('SET_PLAN_LIST', response.rows)
    return response
  }
}
```

### 样式规范

#### 通用样式类
```scss
// 盘点模块专用样式
.stocktaking-container {
  .plan-card {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;

    .plan-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
    }

    .plan-status {
      &.draft { color: #909399; }
      &.pending { color: #e6a23c; }
      &.executing { color: #409eff; }
      &.completed { color: #67c23a; }
      &.cancelled { color: #f56c6c; }
    }
  }

  .task-progress {
    .progress-bar {
      height: 8px;
      border-radius: 4px;
      background-color: #f5f7fa;

      .progress-fill {
        height: 100%;
        border-radius: 4px;
        background: linear-gradient(90deg, #409eff, #67c23a);
        transition: width 0.3s ease;
      }
    }
  }
}
```

### 移动端适配

#### 响应式布局
```vue
<template>
  <div class="stocktaking-mobile">
    <!-- 移动端专用布局 -->
    <div class="mobile-header">
      <el-button @click="$router.go(-1)" icon="el-icon-arrow-left" circle></el-button>
      <span class="title">盘点任务</span>
    </div>

    <!-- 卡片式布局 -->
    <div class="task-cards">
      <div v-for="task in taskList" :key="task.taskId" class="task-card-mobile">
        <!-- 任务信息 -->
      </div>
    </div>
  </div>
</template>

<style scoped>
@media (max-width: 768px) {
  .stocktaking-mobile {
    .mobile-header {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      height: 50px;
      background: #fff;
      border-bottom: 1px solid #e4e7ed;
      display: flex;
      align-items: center;
      padding: 0 15px;
      z-index: 1000;
    }

    .task-cards {
      margin-top: 60px;
      padding: 10px;
    }

    .task-card-mobile {
      background: #fff;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 10px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
  }
}
</style>
```

### 扫码功能实现

#### 扫码组件集成
```vue
<template>
  <div class="asset-scanner">
    <div v-if="!scanning" class="scanner-trigger">
      <el-button type="primary" @click="startScan">
        <i class="el-icon-camera"></i> 扫码盘点
      </el-button>
    </div>

    <div v-else class="scanner-container">
      <div id="scanner-viewport"></div>
      <div class="scanner-controls">
        <el-button @click="stopScan">取消</el-button>
        <el-button type="primary" @click="manualInput">手动输入</el-button>
      </div>
    </div>

    <!-- 扫码结果显示 -->
    <div v-if="scanResult" class="scan-result">
      <el-alert :title="scanResult.message" :type="scanResult.type" show-icon></el-alert>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AssetScanner',
  props: {
    taskId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      scanning: false,
      scanResult: null,
      scanner: null
    }
  },
  methods: {
    async startScan() {
      try {
        // 检查摄像头权限
        const stream = await navigator.mediaDevices.getUserMedia({ video: true })
        this.scanning = true
        this.initScanner()
      } catch (error) {
        this.$modal.msgError('无法访问摄像头，请检查权限设置')
      }
    },

    initScanner() {
      // 初始化扫码器 (使用QuaggaJS或ZXing)
      // 具体实现根据选择的扫码库
    },

    async onScanSuccess(scanContent) {
      try {
        const response = await scanInventory(this.taskId, scanContent)
        this.scanResult = {
          type: 'success',
          message: '扫码成功：' + response.data.assetName
        }
        this.$emit('scan-success', response.data)
      } catch (error) {
        this.scanResult = {
          type: 'error',
          message: '扫码失败：' + error.message
        }
      }
      this.stopScan()
    }
  }
}
</script>
```

### 图表组件实现

#### 进度图表组件
```vue
<template>
  <div class="progress-chart">
    <div class="chart-header">
      <h3>{{ title }}</h3>
      <el-select v-model="chartType" @change="updateChart">
        <el-option label="环形图" value="doughnut"></el-option>
        <el-option label="柱状图" value="bar"></el-option>
        <el-option label="折线图" value="line"></el-option>
      </el-select>
    </div>

    <div class="chart-container">
      <canvas ref="chartCanvas"></canvas>
    </div>

    <div class="chart-legend">
      <div v-for="item in legendData" :key="item.label" class="legend-item">
        <span class="legend-color" :style="{ backgroundColor: item.color }"></span>
        <span class="legend-label">{{ item.label }}</span>
        <span class="legend-value">{{ item.value }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import Chart from 'chart.js/auto'

export default {
  name: 'ProgressChart',
  props: {
    title: {
      type: String,
      default: '盘点进度'
    },
    data: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      chartType: 'doughnut',
      chart: null,
      legendData: []
    }
  },
  mounted() {
    this.initChart()
  },
  watch: {
    data: {
      handler() {
        this.updateChart()
      },
      deep: true
    }
  },
  methods: {
    initChart() {
      const ctx = this.$refs.chartCanvas.getContext('2d')
      this.chart = new Chart(ctx, {
        type: this.chartType,
        data: this.formatChartData(),
        options: this.getChartOptions()
      })
    },

    formatChartData() {
      return {
        labels: ['已完成', '进行中', '未开始'],
        datasets: [{
          data: [
            this.data.completed || 0,
            this.data.inProgress || 0,
            this.data.pending || 0
          ],
          backgroundColor: ['#67c23a', '#409eff', '#e6a23c']
        }]
      }
    }
  }
}
</script>
```

### 数据导出功能

#### Excel导出实现
```javascript
// utils/export.js
import * as XLSX from 'xlsx'

export function exportToExcel(data, filename) {
  const worksheet = XLSX.utils.json_to_sheet(data)
  const workbook = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')
  XLSX.writeFile(workbook, filename + '.xlsx')
}

// 在组件中使用
methods: {
  async exportPlanList() {
    try {
      this.exportLoading = true
      const response = await exportStocktakingPlan(this.queryParams)

      // 格式化导出数据
      const exportData = response.data.map(item => ({
        '计划名称': item.planName,
        '盘点类型': item.planTypeDesc,
        '负责人': item.responsibleUserName,
        '开始日期': item.startDate,
        '结束日期': item.endDate,
        '状态': item.statusDesc,
        '创建时间': item.createTime
      }))

      exportToExcel(exportData, '盘点计划列表')
      this.$modal.msgSuccess('导出成功')
    } catch (error) {
      this.$modal.msgError('导出失败：' + error.message)
    } finally {
      this.exportLoading = false
    }
  }
}
```

### 实时更新机制

#### WebSocket集成
```javascript
// utils/websocket.js
class StocktakingWebSocket {
  constructor() {
    this.ws = null
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
  }

  connect() {
    const wsUrl = process.env.VUE_APP_WS_URL + '/stocktaking'
    this.ws = new WebSocket(wsUrl)

    this.ws.onopen = () => {
      console.log('WebSocket连接成功')
      this.reconnectAttempts = 0
    }

    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data)
      this.handleMessage(data)
    }

    this.ws.onclose = () => {
      console.log('WebSocket连接关闭')
      this.reconnect()
    }
  }

  handleMessage(data) {
    switch (data.type) {
      case 'TASK_PROGRESS_UPDATE':
        this.$bus.$emit('task-progress-updated', data.payload)
        break
      case 'PLAN_STATUS_CHANGE':
        this.$bus.$emit('plan-status-changed', data.payload)
        break
    }
  }
}

// 在组件中使用
mounted() {
  this.$bus.$on('task-progress-updated', this.onTaskProgressUpdated)
},

methods: {
  onTaskProgressUpdated(data) {
    // 更新任务进度
    const task = this.taskList.find(t => t.taskId === data.taskId)
    if (task) {
      task.actualCount = data.actualCount
      task.progress = data.progress
    }
  }
}
```

### 性能优化策略

#### 虚拟滚动实现
```vue
<template>
  <div class="virtual-list" ref="container" @scroll="onScroll">
    <div class="virtual-list-phantom" :style="{ height: totalHeight + 'px' }"></div>
    <div class="virtual-list-content" :style="{ transform: `translateY(${offset}px)` }">
      <div
        v-for="item in visibleData"
        :key="item.id"
        class="virtual-list-item"
        :style="{ height: itemHeight + 'px' }"
      >
        <slot :item="item"></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'VirtualList',
  props: {
    data: Array,
    itemHeight: {
      type: Number,
      default: 50
    },
    visibleCount: {
      type: Number,
      default: 10
    }
  },
  data() {
    return {
      scrollTop: 0,
      startIndex: 0,
      endIndex: 0
    }
  },
  computed: {
    totalHeight() {
      return this.data.length * this.itemHeight
    },
    visibleData() {
      return this.data.slice(this.startIndex, this.endIndex)
    },
    offset() {
      return this.startIndex * this.itemHeight
    }
  },
  methods: {
    onScroll() {
      this.scrollTop = this.$refs.container.scrollTop
      this.updateVisibleData()
    },

    updateVisibleData() {
      this.startIndex = Math.floor(this.scrollTop / this.itemHeight)
      this.endIndex = this.startIndex + this.visibleCount
    }
  }
}
</script>
```

### 测试用例示例

#### 单元测试
```javascript
// tests/unit/components/AssetScanner.spec.js
import { shallowMount } from '@vue/test-utils'
import AssetScanner from '@/views/assets/stocktaking/components/AssetScanner.vue'

describe('AssetScanner.vue', () => {
  let wrapper

  beforeEach(() => {
    wrapper = shallowMount(AssetScanner, {
      propsData: {
        taskId: 'test-task-id'
      }
    })
  })

  it('renders correctly', () => {
    expect(wrapper.find('.asset-scanner').exists()).toBe(true)
  })

  it('shows scan button initially', () => {
    expect(wrapper.find('.scanner-trigger').exists()).toBe(true)
    expect(wrapper.vm.scanning).toBe(false)
  })

  it('emits scan-success event when scan succeeds', async () => {
    const mockScanData = { assetId: 'test-asset', assetName: 'Test Asset' }
    await wrapper.vm.onScanSuccess('test-scan-content')

    expect(wrapper.emitted('scan-success')).toBeTruthy()
    expect(wrapper.emitted('scan-success')[0][0]).toEqual(mockScanData)
  })
})
```

### 部署配置

#### 环境配置
```javascript
// .env.development
VUE_APP_BASE_API = 'http://localhost:8080'
VUE_APP_WS_URL = 'ws://localhost:8080'

// .env.production
VUE_APP_BASE_API = 'https://api.example.com'
VUE_APP_WS_URL = 'wss://api.example.com'
```

#### 构建优化
```javascript
// vue.config.js
module.exports = {
  configureWebpack: {
    optimization: {
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          stocktaking: {
            name: 'chunk-stocktaking',
            test: /[\\/]views[\\/]assets[\\/]stocktaking/,
            minChunks: 1,
            priority: 20,
            reuseExistingChunk: true
          }
        }
      }
    }
  }
}
```

---

**文档版本**: V1.0
**创建日期**: 2025-01-14
**适用范围**: AI前端开发任务执行
**最后更新**: 2025-01-14
